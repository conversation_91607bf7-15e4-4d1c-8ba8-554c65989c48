<template>
  <div class="file-upload-container">
    <el-upload
      ref="uploadRef"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :accept="accept"
      :show-file-list="showFileList"
      :auto-upload="autoUpload"
      :limit="limit"
      :file-list="internalFileList"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :disabled="disabled"
      class="file-upload"
      :class="{ 'is-dragover': isDragover }"
      drag
    >
      <div class="upload-content">
        <el-icon class="upload-icon" :size="48">
          <UploadFilled />
        </el-icon>
        <div class="upload-text">
          <p class="upload-title">点击或拖拽文件到此处上传</p>
          <p class="upload-hint">支持 {{ acceptText }} 格式</p>
        </div>
      </div>
    </el-upload>

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress
        :percentage="uploadProgress"
        :status="uploadStatus"
        :stroke-width="6"
      />
      <p class="progress-text">
        {{ uploadStatusText }}
      </p>
    </div>

    <!-- 文件列表 -->
    <div
      v-if="showCustomFileList && internalFileList.length > 0"
      class="file-list"
    >
      <div class="file-list-header">
        <span>已上传文件 ({{ internalFileList.length }})</span>
        <el-button
          v-if="internalFileList.length > 1"
          text
          size="small"
          @click="clearAll"
        >
          清空全部
        </el-button>
      </div>

      <div class="file-items">
        <div
          v-for="(file, index) in internalFileList"
          :key="file.uid || index"
          class="file-item"
        >
          <div class="file-info">
            <el-icon class="file-icon">
              <component :is="getFileIcon(file.name)" />
            </el-icon>
            <div class="file-details">
              <span class="file-name" :title="file.name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
            </div>
          </div>

          <div class="file-actions">
            <el-button
              v-if="canPreview(file)"
              text
              size="small"
              @click="previewFile(file)"
            >
              预览
            </el-button>
            <el-button text size="small" @click="downloadFile(file)">
              下载
            </el-button>
            <el-button
              text
              size="small"
              type="danger"
              @click="removeFile(file)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import {
  UploadFilled,
  DocumentCopy,
  Document,
  Picture,
  Folder,
} from "@element-plus/icons-vue";
import { filesAPI } from "@/api/files";

// 定义 props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: ".pdf",
  },
  maxSize: {
    type: Number,
    default: 1024 * 1024 * 1024, // 1GB - 实际上不限制大小
  },
  limit: {
    type: Number,
    default: 1,
  },
  autoUpload: {
    type: Boolean,
    default: true,
  },
  showFileList: {
    type: Boolean,
    default: false,
  },
  showCustomFileList: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// 定义 emits
const emit = defineEmits([
  "update:modelValue",
  "success",
  "error",
  "progress",
  "exceed",
]);

// 上传相关
const uploadRef = ref();
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref("");
const isDragover = ref(false);

// 文件列表
const internalFileList = ref([...props.modelValue]);

// 上传配置
const uploadAction = computed(() => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
  return `${baseURL}/files/upload`;
});

const uploadHeaders = computed(() => {
  const token = localStorage.getItem("token");
  return token ? { Authorization: `Bearer ${token}` } : {};
});

const uploadData = computed(() => ({}));

// 接受的文件类型文本
const acceptText = computed(() => {
  return props.accept.replace(/\./g, "").toUpperCase();
});

// 最大文件大小文本
const maxSizeText = computed(() => {
  return filesAPI.formatFileSize(props.maxSize);
});

// 上传状态文本
const uploadStatusText = computed(() => {
  if (uploadStatus.value === "success") {
    return "上传成功";
  } else if (uploadStatus.value === "exception") {
    return "上传失败";
  } else {
    return `上传中... ${uploadProgress.value}%`;
  }
});

// 防止递归更新的标志
const isUpdatingFromParent = ref(false);

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (!isUpdatingFromParent.value) {
      isUpdatingFromParent.value = true;
      internalFileList.value = [...newValue];
      nextTick(() => {
        isUpdatingFromParent.value = false;
      });
    }
  },
  { deep: true },
);

// 监听内部文件列表变化
watch(
  () => internalFileList.value,
  (newValue) => {
    if (!isUpdatingFromParent.value) {
      emit("update:modelValue", newValue);
    }
  },
  { deep: true },
);

// 上传前验证

// 上传进度
const handleProgress = (event, file) => {
  uploadProgress.value = Math.round(event.percent);
  emit("progress", uploadProgress.value, file);
};

// 上传成功
const handleSuccess = (response, file) => {
  uploading.value = false;
  uploadStatus.value = "success";

  if (response.success) {
    const fileInfo = {
      uid: file.uid,
      name: response.data.originalName,
      filename: response.data.filename,
      size: response.data.size,
      url: response.data.url,
      uploadedAt: response.data.uploadedAt,
    };

    internalFileList.value.push(fileInfo);
    emit("success", fileInfo, response);
    ElMessage.success("文件上传成功");
  } else {
    uploadStatus.value = "exception";
    emit("error", new Error(response.message || "上传失败"), file);
    ElMessage.error(response.message || "文件上传失败");
  }

  // 重置进度
  setTimeout(() => {
    uploadProgress.value = 0;
    uploadStatus.value = "";
  }, 2000);
};

// 上传失败 - 保留用户反馈，简化错误处理
const handleError = (error, file) => {
  uploading.value = false;
  uploadStatus.value = "exception";

  emit("error", error, file);
  // 全局错误处理器会处理网络错误，这里只处理上传特定的错误
  ElMessage.error("文件上传失败");

  // 重置进度
  setTimeout(() => {
    uploadProgress.value = 0;
    uploadStatus.value = "";
  }, 2000);
};

// 文件数量超出限制
const handleExceed = (files) => {
  emit("exceed", files);
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`);
};

// 移除文件
const handleRemove = (file) => {
  const index = internalFileList.value.findIndex(
    (item) => item.uid === file.uid,
  );
  if (index > -1) {
    internalFileList.value.splice(index, 1);
  }
};

// 获取文件图标
const getFileIcon = (filename) => {
  const iconName = filesAPI.getFileIcon(filename);
  const iconMap = {
    DocumentCopy,
    Document,
    Picture,
    Folder,
  };
  return iconMap[iconName] || Document;
};

// 格式化文件大小
const formatFileSize = (size) => {
  return filesAPI.formatFileSize(size);
};

// 是否可以预览
const canPreview = (file) => {
  return file.name.toLowerCase().endsWith(".pdf");
};

// 预览文件
const previewFile = (file) => {
  const filename = file.filename || file.response?.filename;
  if (filename) {
    // 创建带token的预览URL - 使用临时预览API，适用于刚上传的文件
    const token = localStorage.getItem("token");
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
    const previewUrl = `${baseURL}/files/temp-preview/${filename}${token ? `?token=${token}` : ""}`;
    window.open(previewUrl, "_blank");
  } else {
    ElMessage.warning("文件信息不完整，无法预览");
  }
};

// 下载文件
const downloadFile = async (file) => {
  try {
    const filename = file.filename || file.response?.filename;
    if (filename) {
      // 使用临时下载API，适用于刚上传的文件
      const blob = await filesAPI.tempDownload(filename);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = file.name || filename;
      link.click();
      window.URL.revokeObjectURL(url);
      ElMessage.success("文件下载成功");
    } else {
      ElMessage.warning("文件信息不完整，无法下载");
    }
  } catch (error) {
    console.error("文件下载失败:", error);
    if (error.response?.status === 403) {
      ElMessage.error("没有权限下载此文件");
    } else if (error.response?.status === 404) {
      ElMessage.error("文件不存在");
    } else {
      ElMessage.error("文件下载失败，请稍后重试");
    }
  }
};

// 移除文件
const removeFile = async (file) => {
  try {
    // 先从列表中移除，无论后端删除是否成功
    const index = internalFileList.value.findIndex(
      (item) => item.uid === file.uid,
    );
    if (index > -1) {
      internalFileList.value.splice(index, 1);
    }

    // 获取文件名，优先使用filename，其次使用response中的filename
    const filename = file.filename || file.response?.filename;

    // 如果有filename，尝试从后端删除
    if (filename) {
      try {
        // 使用临时删除API，适用于刚上传的文件
        await filesAPI.tempDelete(filename);
        ElMessage.success("文件删除成功");
      } catch (error) {
        console.error("后端文件删除失败:", error);
        if (error.response?.status === 403) {
          ElMessage.warning("文件已从列表中移除，但没有权限删除服务器上的文件");
        } else if (error.response?.status === 404) {
          ElMessage.warning("文件已从列表中移除，服务器上的文件不存在");
        } else {
          ElMessage.warning("文件已从列表中移除，但服务器删除失败");
        }
      }
    } else {
      ElMessage.success("文件已从列表中移除");
    }
  } catch (error) {
    console.error("文件删除失败:", error);
    ElMessage.error("文件删除失败");
  }
};

// 清空所有文件
const clearAll = () => {
  internalFileList.value = [];
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 手动上传
const submit = () => {
  if (uploadRef.value) {
    uploadRef.value.submit();
  }
};

// 清空文件
const clearFiles = () => {
  clearAll();
};

// 暴露方法
defineExpose({
  submit,
  clearFiles,
});
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.file-upload {
  width: 100%;
}

/* 专业上传区域样式 */
.upload-content {
  padding: 60px 40px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px dashed #d1ecf1;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.upload-content:hover::before {
  left: 100%;
}

.upload-content:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

.upload-icon {
  color: #409eff;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(64, 158, 255, 0.2));
}

.upload-content:hover .upload-icon {
  transform: scale(1.1);
  color: #36a3f7;
}

.upload-title {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.upload-hint {
  font-size: 14px;
  color: #606266;
  margin: 0;
  font-weight: 400;
}

.upload-progress {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.progress-text {
  text-align: center;
  font-size: 14px;
  color: #606266;
  margin: 8px 0 0;
}

.file-list {
  margin-top: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

/* 专业文件列表样式 */
.file-items {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  transition: all 0.3s ease;
  background: white;
  margin: 8px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.file-item:hover {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  border-color: #409eff;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  gap: 16px;
}

.file-icon {
  font-size: 28px;
  color: #409eff;
  flex-shrink: 0;
  padding: 8px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.file-item:hover .file-icon {
  transform: scale(1.1);
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
}

.file-details {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
}

.file-name {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #606266;
  background: #f1f3f4;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.file-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.file-actions .el-button {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.file-actions .el-button:hover {
  transform: translateY(-1px);
}

/* Element Plus 样式覆盖 */
:deep(.el-upload-dragger) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: border-color 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

:deep(.el-upload-dragger.is-dragover) {
  border-color: #409eff;
  background: #ecf5ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-content {
    padding: 30px 16px;
  }

  .upload-title {
    font-size: 14px;
  }

  .upload-hint {
    font-size: 12px;
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>

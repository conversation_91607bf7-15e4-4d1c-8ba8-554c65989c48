<template>
  <div class="submit-page">
    <!-- 专业页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <h1 class="page-title">
            <el-icon class="title-icon"><DocumentAdd /></el-icon>
            合同提交
          </h1>
          <p class="page-subtitle">安全、高效的合同审核流程</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-label">当前步骤</span>
            <span class="stat-value">{{ currentStep + 1 }}/3</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">完成进度</span>
            <span class="stat-value">{{ Math.round(((currentStep + 1) / 3) * 100) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="submit-container">
        <!-- 步骤指示器 -->
        <div class="steps-container">
          <el-steps
            :active="currentStep"
            finish-status="success"
            process-status="process"
            class="custom-steps"
          >
            <el-step
              title="上传文件"
              description="选择并上传合同PDF文件"
              :icon="Upload"
            />
            <el-step
              title="填写信息"
              description="选择审核级别和审核人员"
              :icon="Edit"
            />
            <el-step
              title="提交审核"
              description="确认信息并提交审核"
              :icon="Check"
            />
          </el-steps>
        </div>

        <!-- 表单卡片 -->
        <el-card class="submit-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="card-title-section">
                <h3 class="card-title">{{ getStepTitle() }}</h3>
                <p class="card-subtitle">{{ getStepDescription() }}</p>
              </div>
              <div class="card-progress">
                <el-progress
                  :percentage="Math.round(((currentStep + 1) / 3) * 100)"
                  :stroke-width="6"
                  :show-text="false"
                  color="#409eff"
                />
              </div>
            </div>
          </template>

          <el-form
            ref="submitFormRef"
            :model="submitForm"
            :rules="submitRules"
            label-width="120px"
            size="default"
          >
            <!-- 步骤1：文件上传 -->
            <div v-show="currentStep === 0" class="step-content">
              <!-- 直接PDF文件上传 -->
              <el-form-item label="合同文件" prop="files" required>
                <FileUpload
                  v-model="submitForm.files"
                  :limit="1"
                  accept=".pdf"
                  :max-size="1024 * 1024 * 1024"
                  @success="handleFileUploadSuccess"
                  @error="handleFileUploadError"
                />
                <div class="upload-tips">
                  <el-icon><InfoFilled /></el-icon>
                  <span>请上传PDF格式的合同文件，文件大小不超过1GB</span>
                </div>
              </el-form-item>
            </div>

            <!-- 步骤2：填写信息 -->
            <div v-show="currentStep === 1" class="step-content">
              <el-form-item label="审核级别" prop="review_level" required>
                <el-select
                  v-model="submitForm.review_level"
                  placeholder="请选择审核级别"
                  style="width: 100%"
                  @change="handleReviewLevelChange"
                >
                  <el-option label="县局审核员" value="county_reviewer" />
                </el-select>
                <div class="form-tips">
                  <el-icon><InfoFilled /></el-icon>
                  <span
                    >合同将提交给县局审核员，由县局审核员决定是否需要市局审核</span
                  >
                </div>
              </el-form-item>

              <el-form-item label="审核员" prop="reviewer_id" required>
                <el-select
                  v-model="submitForm.reviewer_id"
                  placeholder="请先选择审核级别"
                  style="width: 100%"
                  :disabled="!submitForm.review_level"
                  :loading="loadingReviewers"
                >
                  <el-option
                    v-for="reviewer in filteredReviewers"
                    :key="reviewer.id"
                    :label="`${reviewer.username} (${getRoleDisplayName(reviewer.role)})`"
                    :value="reviewer.id"
                  />
                </el-select>
                <div class="form-tips">
                  <el-icon><InfoFilled /></el-icon>
                  <span>选择具体的审核员</span>
                </div>
              </el-form-item>

              <el-form-item label="提交说明" prop="submit_note">
                <el-input
                  v-model="submitForm.submit_note"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入合同的相关说明信息（可选）"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </div>

            <!-- 步骤3：确认提交 -->
            <div v-show="currentStep === 2" class="step-content">
              <div class="submit-summary">
                <h4>提交信息确认</h4>
                <div class="summary-item">
                  <span class="label">合同文件：</span>
                  <span class="value">{{ getFileName() }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">文件大小：</span>
                  <span class="value">{{ getFileSize() }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">审核级别：</span>
                  <span class="value">{{ getReviewLevelName() }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">审核员：</span>
                  <span class="value">{{ getReviewerName() }}</span>
                </div>
                <div v-if="submitForm.submit_note" class="summary-item">
                  <span class="label">提交说明：</span>
                  <span class="value">{{ submitForm.submit_note }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
              <el-button
                v-if="currentStep > 0"
                :disabled="submitting"
                @click="prevStep"
              >
                上一步
              </el-button>

              <el-button
                v-if="currentStep < 2"
                type="primary"
                :disabled="!canNextStep()"
                @click="nextStep"
              >
                下一步
              </el-button>

              <el-button
                v-if="currentStep === 2"
                type="primary"
                :loading="submitting"
                @click="handleSubmit"
              >
                {{ submitting ? "提交中..." : "确认提交" }}
              </el-button>

              <el-button :disabled="submitting" @click="resetForm">
                重置
              </el-button>
            </div>
          </el-form>
        </el-card>

        <!-- 提交成功提示 -->
        <el-card v-if="submitSuccess" class="success-card">
          <div class="success-content">
            <el-icon class="success-icon" :size="64">
              <CircleCheckFilled />
            </el-icon>
            <h3 class="success-title">合同提交成功！</h3>
            <p class="success-description">
              您的合同已成功提交，流水号为：<strong>{{
                successInfo.serial_number
              }}</strong>
            </p>
            <p class="success-tips">
              审核人员将在收到通知后开始审核，您可以在"我的合同"中查看审核进度。
            </p>
            <div class="success-actions">
              <el-button type="primary" @click="goToMyContracts">
                查看我的合同
              </el-button>
              <el-button @click="submitAnother"> 继续提交 </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  InfoFilled,
  CircleCheckFilled,
  DocumentAdd,
  Upload,
  Edit,
  Check
} from "@element-plus/icons-vue";

import FileUpload from "@/components/common/FileUpload.vue";
import { useContracts } from "@/composables/useContracts";
import { contractsAPI } from "@/api/contracts";
import { filesAPI } from "@/api/files";

const router = useRouter();

// 合同管理
const { submitContract, submitting } = useContracts();

// 表单引用
const submitFormRef = ref();

// 当前步骤
const currentStep = ref(0);

// 审核级别选项 - 员工只能选择县局审核员
const reviewLevelOptions = [{ value: "county_reviewer", label: "县局审核员" }];

// 提交成功状态
const submitSuccess = ref(false);
const successInfo = ref({});

// 表单数据 - 默认设置为县级审核员
const submitForm = reactive({
  files: [],
  review_level: "county_reviewer",
  reviewer_id: "",
  submit_note: "",
});

// 审核员相关状态
const allReviewers = ref([]); // 缓存所有审核员
const loadingReviewers = ref(false);
const reviewersLoaded = ref(false); // 标记是否已加载过审核员数据

// 表单验证规则
const submitRules = {
  files: [{ required: true, message: "请上传合同文件", trigger: "change" }],
  review_level: [
    { required: true, message: "请选择审核级别", trigger: "change" },
  ],
  reviewer_id: [{ required: true, message: "请选择审核员", trigger: "change" }],
  submit_note: [
    { max: 500, message: "提交说明不能超过500个字符", trigger: "blur" },
  ],
};

// 获取审核级别名称
const getReviewLevelName = () => {
  const option = reviewLevelOptions.find(
    (opt) => opt.value === submitForm.review_level,
  );
  return option ? option.label : "";
};

// 获取审核员名称
const getReviewerName = () => {
  const reviewer = allReviewers.value.find(
    (r) => r.id === submitForm.reviewer_id,
  );
  return reviewer
    ? `${reviewer.username} (${getRoleDisplayName(reviewer.role)})`
    : "";
};

// 获取角色显示名称
const getRoleDisplayName = (role) => {
  const roleMap = {
    admin: "管理员",
    county_reviewer: "县级审核员",
    city_reviewer: "市级审核员",
  };
  return roleMap[role] || role;
};

// 计算属性：根据审核级别动态过滤审核员
const filteredReviewers = computed(() => {
  if (!submitForm.review_level || !allReviewers.value.length) {
    return [];
  }

  if (submitForm.review_level === "county_reviewer") {
    return allReviewers.value.filter(
      (reviewer) => reviewer.role === "county_reviewer",
    );
  }

  return allReviewers.value;
});

// 预加载所有审核员数据
const loadAllReviewers = async () => {
  if (reviewersLoaded.value) return; // 已加载过，直接返回

  try {
    loadingReviewers.value = true;
    // 不传level参数，获取所有审核员
    const response = await contractsAPI.getReviewers();
    if (response.success) {
      allReviewers.value = response.data;
      reviewersLoaded.value = true;
    }
  } catch (error) {
    console.error("获取审核员列表失败:", error);
    ElMessage.error("获取审核员列表失败");
  } finally {
    loadingReviewers.value = false;
  }
};

// 处理审核级别变化
const handleReviewLevelChange = async (level) => {
  // 清空当前选择的审核员
  submitForm.reviewer_id = "";

  // 确保已加载所有审核员数据（如果还没加载的话）
  if (!reviewersLoaded.value) {
    await loadAllReviewers();
  }

  // 计算属性会自动根据审核级别过滤审核员，无需手动过滤
};

// 检查是否可以进入下一步
const canNextStep = () => {
  switch (currentStep.value) {
    case 0:
      return submitForm.files.length > 0;
    case 1:
      return submitForm.review_level;
    default:
      return false;
  }
};

// 下一步
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 验证文件上传
    if (submitForm.files.length === 0) {
      ElMessage.warning("请先上传合同文件");
      return;
    }
  } else if (currentStep.value === 1) {
    // 验证表单
    try {
      await submitFormRef.value.validateField([
        "review_level",
        "reviewer_id",
        "submit_note",
      ]);
    } catch (error) {
      return;
    }
  }

  currentStep.value++;
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 处理文件上传成功
const handleFileUploadSuccess = (fileInfo) => {
  // 文件上传成功，但不自动跳转，让用户确认后手动点击下一步
  ElMessage.success("文件上传成功，请确认后点击下一步");
};

// 处理文件上传失败 - 简化错误处理，依赖全局错误处理器
const handleFileUploadError = (error) => {
  // 全局错误处理器会处理网络错误，这里只需要给用户友好提示
  ElMessage.error("文件上传失败，请重试");
};

// 获取文件名
const getFileName = () => {
  return submitForm.files.length > 0 ? submitForm.files[0].name : "";
};

// 获取文件大小
const getFileSize = () => {
  if (submitForm.files.length > 0) {
    return filesAPI.formatFileSize(submitForm.files[0].size);
  }
  return "";
};

// 提交合同
const handleSubmit = async () => {
  try {
    // 最终验证
    await submitFormRef.value.validate();

    if (submitForm.files.length === 0) {
      ElMessage.error("请上传合同文件");
      return;
    }

    const file = submitForm.files[0];

    // 构建提交数据
    const contractData = {
      filename: file.name || file.originalName, // 原始文件名
      file_path: file.filename, // 服务器生成的文件名
      file_size: file.size,
      review_level: submitForm.review_level,
      reviewer_id: submitForm.reviewer_id,
      submit_note: submitForm.submit_note || null,
    };

    // 提交合同
    const result = await submitContract(contractData);

    if (result) {
      successInfo.value = result;
      submitSuccess.value = true;
      currentStep.value = 0;

      // 延迟重置表单，避免与成功状态更新冲突
      nextTick(() => {
        resetForm();
      });
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("提交合同失败:", error);
    }
    ElMessage.error(error.message || "提交合同失败");
  }
};

// 重置表单
const resetForm = () => {
  // 先清除验证状态，避免触发验证错误
  submitFormRef.value?.clearValidate();

  // 重置表单字段
  nextTick(() => {
    submitFormRef.value?.resetFields();
    submitForm.files = [];
    submitForm.review_level = "";
    submitForm.reviewer_id = "";
    submitForm.submit_note = "";
    // 不需要清空 allReviewers，保持缓存
    currentStep.value = 0;
    submitSuccess.value = false;
    successInfo.value = {};
  });
};

// 跳转到我的合同
const goToMyContracts = () => {
  router.push("/my-contracts");
};

// 继续提交
const submitAnother = () => {
  resetForm();
};

// 获取当前步骤标题
const getStepTitle = () => {
  const titles = ['上传合同文件', '填写审核信息', '确认提交信息'];
  return titles[currentStep.value] || '合同提交';
};

// 获取当前步骤描述
const getStepDescription = () => {
  const descriptions = [
    '请选择并上传PDF格式的合同文件',
    '选择合适的审核级别和审核人员',
    '确认所有信息无误后提交审核'
  ];
  return descriptions[currentStep.value] || '';
};

// 组件挂载时的初始化
onMounted(() => {
  // 预加载审核员数据，提升用户体验
  loadAllReviewers();
});
</script>

<style scoped>
.submit-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 专业页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-main {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  opacity: 0.9;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-label {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: white;
}

/* 主要内容区域 */
.page-content {
  padding: 40px 24px;
}

.submit-container {
  max-width: 900px;
  margin: 0 auto;
}

/* 步骤指示器样式 */
.steps-container {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.custom-steps {
  margin: 0;
}

.custom-steps :deep(.el-step__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.custom-steps :deep(.el-step__description) {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.custom-steps :deep(.el-step__icon) {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-steps :deep(.el-step.is-process .el-step__icon) {
  background: linear-gradient(135deg, #409eff, #36a3f7);
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.custom-steps :deep(.el-step.is-finish .el-step__icon) {
  background: linear-gradient(135deg, #67c23a, #5daf34);
  color: white;
}

/* 表单卡片样式 */
.submit-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.submit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1px solid #e9ecef;
  margin: -20px -20px 0 -20px;
  padding: 24px;
}

.card-title-section {
  flex: 1;
}

.card-title {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  color: #606266;
  font-weight: 400;
}

.card-progress {
  width: 200px;
  margin-left: 24px;
}

/* 步骤内容样式 */
.step-content {
  min-height: 300px;
  padding: 32px 0;
  position: relative;
}

.step-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -24px;
  right: -24px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e9ecef, transparent);
}

/* 表单提示样式优化 */
.upload-mode-tips,
.upload-tips,
.form-tips {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid #bae6fd;
  border-radius: 12px;
  font-size: 14px;
  color: #0369a1;
  box-shadow: 0 2px 8px rgba(3, 105, 161, 0.1);
  transition: all 0.2s ease;
}

.upload-mode-tips:hover,
.upload-tips:hover,
.form-tips:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(3, 105, 161, 0.15);
}

.upload-mode-tips .el-icon,
.upload-tips .el-icon,
.form-tips .el-icon {
  font-size: 18px;
  color: #0284c7;
}

/* 提交确认样式 */
.submit-summary {
  padding: 32px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16px;
  border: 1px solid #dee2e6;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.submit-summary h4 {
  margin: 0 0 24px;
  font-size: 18px;
  font-weight: 700;
  color: #303133;
  text-align: center;
  position: relative;
}

.submit-summary h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #409eff, #36a3f7);
  border-radius: 2px;
}

.summary-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.summary-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item .label {
  width: 140px;
  font-weight: 600;
  color: #495057;
  flex-shrink: 0;
  position: relative;
}

.summary-item .label::after {
  content: ':';
  margin-left: 4px;
  color: #6c757d;
}

.summary-item .value {
  color: #212529;
  font-weight: 500;
  word-break: break-all;
  flex: 1;
}

/* 操作按钮样式 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 2px solid #e9ecef;
  position: relative;
}

.form-actions::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(135deg, #409eff, #36a3f7);
  border-radius: 1px;
}

.form-actions .el-button {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.form-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.form-actions .el-button--primary {
  background: linear-gradient(135deg, #409eff, #36a3f7);
  border: none;
}

.form-actions .el-button--primary:hover {
  background: linear-gradient(135deg, #36a3f7, #2d8cf0);
}

.success-card {
  border: 2px solid #67c23a;
}

.success-content {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  color: #67c23a;
  margin-bottom: 16px;
}

.success-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px;
}

.success-description {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px;
}

.success-tips {
  font-size: 14px;
  color: #909399;
  margin: 0 0 24px;
}

.success-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* 专业响应式设计 */
@media (max-width: 1200px) {
  .submit-container {
    max-width: 100%;
    padding: 0 16px;
  }

  .header-stats {
    gap: 16px;
  }

  .stat-item {
    padding: 12px 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
  }

  .page-title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 28px;
  }

  .header-stats {
    width: 100%;
    justify-content: space-around;
  }

  .page-content {
    padding: 24px 16px;
  }

  .steps-container {
    padding: 24px 16px;
  }

  .custom-steps :deep(.el-step__title) {
    font-size: 14px;
  }

  .custom-steps :deep(.el-step__description) {
    font-size: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    padding: 20px;
  }

  .card-progress {
    width: 100%;
    margin-left: 0;
  }

  .step-content {
    padding: 24px 0;
  }

  .submit-summary {
    padding: 24px 16px;
  }

  .summary-item {
    flex-direction: column;
    gap: 8px;
    padding: 12px 16px;
  }

  .summary-item .label {
    width: auto;
    margin-bottom: 4px;
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;
  }

  .form-actions .el-button {
    width: 100%;
    padding: 14px 24px;
  }

  .success-actions {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }

  .stat-item {
    padding: 8px 12px;
  }

  .stat-value {
    font-size: 16px;
  }

  .steps-container {
    padding: 16px 12px;
  }

  .card-header {
    padding: 16px;
  }

  .card-title {
    font-size: 18px;
  }

  .step-content {
    padding: 20px 0;
  }

  .submit-summary {
    padding: 20px 12px;
  }

  .summary-item {
    padding: 12px;
  }
}
</style>
